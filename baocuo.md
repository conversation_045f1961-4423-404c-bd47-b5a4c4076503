                                                                                                    	at com.example.aimusicplayer.service.PlayerControllerImpl.replaceAll(PlayerControllerImpl.kt:129)
                                                                                                    	at com.example.aimusicplayer.viewmodel.PlayerViewModel$loadCachedPlaylist$1.invokeSuspend(PlayerViewModel.kt:506)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 20:37:42.078  3144-5593  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:42.082  3144-5609  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:42.083  3144-5599  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:42.090  3144-3461  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:42.944  3144-5571  ExoPlayerImplInternal   com.example.aimusicplayer            E  Playback error (Ask Gemini)
                                                                                                      androidx.media3.exoplayer.ExoPlaybackException: Source error
                                                                                                          at androidx.media3.exoplayer.ExoPlayerImplInternal.handleIoException(ExoPlayerImplInternal.java:701)
                                                                                                          at androidx.media3.exoplayer.ExoPlayerImplInternal.handleMessage(ExoPlayerImplInternal.java:671)
                                                                                                          at android.os.Handler.dispatchMessage(Handler.java:102)
                                                                                                          at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                          at android.os.Looper.loop(Looper.java:288)
                                                                                                          at android.os.HandlerThread.run(HandlerThread.java:67)
                                                                                                      Caused by: androidx.media3.exoplayer.source.UnrecognizedInputFormatException: None of the available extractors (FlvExtractor, FlacExtractor, WavExtractor, FragmentedMp4Extractor, Mp4Extractor, AmrExtractor, PsExtractor, OggExtractor, TsExtractor, MatroskaExtractor, AdtsExtractor, Ac3Extractor, Ac4Extractor, Mp3Extractor, AviExtractor, JpegExtractor, PngExtractor, WebpExtractor, BmpExtractor, HeifExtractor) could read the stream.{contentIsMalformed=false, dataType=1}
                                                                                                          at androidx.media3.exoplayer.source.BundledExtractorsAdapter.init(BundledExtractorsAdapter.java:94)
                                                                                                          at androidx.media3.exoplayer.source.ProgressiveMediaPeriod$ExtractingLoadable.load(ProgressiveMediaPeriod.java:1044)
                                                                                                          at androidx.media3.exoplayer.upstream.Loader$LoadTask.run(Loader.java:417)
                                                                                                          at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                          at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                          at java.lang.Thread.run(Thread.java:1012)
2025-05-24 20:37:42.959  3144-5585  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:42.971  3144-5595  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:42.977  3144-5613  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:42.977  3144-5612  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:42.986  3144-3462  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:43.085  3144-5594  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:43.091  3144-5622  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:43.091  3144-5609  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:43.104  3144-5626  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:43.127  3144-5578  ImageUtils              com.example.aimusicplayer            E  加载图片失败:  (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.doGet(RequestFutureTarget.java:217)
                                                                                                    	at com.bumptech.glide.request.RequestFutureTarget.get(RequestFutureTarget.java:130)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invokeSuspend(ImageUtils.kt:275)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:8)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadBitmapFromUri$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.loadBitmapFromUri(ImageUtils.kt:269)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils$loadAndProcessAlbumCover$2.invokeSuspend(ImageUtils.kt:295)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: com.bumptech.glide.load.engine.GlideException: Failed to load resource
2025-05-24 20:37:43.244  3144-3144  AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: main (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 3144
                                                                                                    android.renderscript.RSIllegalArgumentException: Unsupported element type.
                                                                                                    	at android.renderscript.ScriptIntrinsicBlur.setInput(ScriptIntrinsicBlur.java:70)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.createBlurredBitmap(ImageUtils.kt:188)
                                                                                                    	at com.example.aimusicplayer.utils.ImageUtils.blurBitmap(ImageUtils.kt:205)
                                                                                                    	at com.example.aimusicplayer.ui.player.PlayerFragment$updateAlbumArt$2$2$blurredBitmap$1.invokeSuspend(PlayerFragment.kt:566)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@b2b77b7, Dispatchers.Main.immediate]
2025-05-24 20:37:43.371   613-1339  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 20:37:43.720  2233-2357  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 20:37:43.733  2233-2233  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-24 20:37:43.769  2233-2357  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 20:37:43.825  2233-2357  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 20:37:43.850  2233-2233  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-24 20:37:43.850  2233-2233  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=1
2025-05-24 20:37:43.995   364-364   ClientCache             surfaceflinger                       E  failed to get buffer, invalid process token
2025-05-24 20:37:43.995   364-364   BpTransact...edListener surfaceflinger                       E  Failed to transact (-32)
2025-05-24 20:37:44.022  2233-2233  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-24 20:37:44.030  2233-2357  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 20:37:44.118  4806-4806  droid.apps.maps         com.google.android.apps.maps         E  No package ID ff found for ID 0xffffffff.
2025-05-24 20:37:44.476  4806-4909  OpenGLRenderer          com.google.android.apps.maps         E  Unable to match the desired swap behavior.
2025-05-24 20:37:44.486  1955-2722  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-24 20:37:44.683  2233-2233  SurfaceSyncer           com.android.car.carlauncher          E  Failed to find sync for id=0
2025-05-24 20:37:44.712  2233-2357  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 20:37:45.023  4806-4909  OpenGLRenderer          com.google.android.apps.maps         E  Unable to match the desired swap behavior.
2025-05-24 20:37:47.011   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:37:47.011   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:37:47.011   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:37:49.632  1061-1061  ConstraintLayout        com.android.systemui                 E  layout_constraintHeight_default="wrap" is deprecated.
                                                                                                    Use layout_height="WRAP_CONTENT" and layout_constrainedHeight="true" instead.
2025-05-24 20:37:50.173  1061-1241  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-24 20:37:50.602  1061-1061  ConstraintLayout        com.android.systemui                 E  layout_constraintHeight_default="wrap" is deprecated.
                                                                                                    Use layout_height="WRAP_CONTENT" and layout_constrainedHeight="true" instead.
2025-05-24 20:37:50.611  1061-1061  ConstraintLayout        com.android.systemui                 E  layout_constraintHeight_default="wrap" is deprecated.
                                                                                                    Use layout_height="WRAP_CONTENT" and layout_constrainedHeight="true" instead.
2025-05-24 20:37:50.937  1061-1241  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-24 20:37:52.157   613-991   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 20:37:52.233   613-2812  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 20:37:52.451  2233-2357  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 20:37:52.809  1542-4088  bnan                    com.google.android.gms.persistent    E  Phenotype registration failed [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@242632114@24.26.32 (230800-650348549):53)
                                                                                                    	at bnan.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):68)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):47)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126)
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 20:37:53.204  2233-2357  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 20:37:55.209   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:37:55.210   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:37:55.210   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:37:56.786   613-784   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 20:37:57.074   613-961   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 20:37:57.334  2233-2357  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 20:37:59.292  5778-5802  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 20:38:00.363   613-1360  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 20:38:01.109   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:38:01.109   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:38:01.109   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:38:01.576  5778-5802  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 20:38:02.038  5778-5778  UnifiedPlaybackService  com.example.aimusicplayer            E  onStartCommand执行失败 (Ask Gemini)
                                                                                                    kotlin.UninitializedPropertyAccessException: lateinit property player has not been initialized
                                                                                                    	at com.example.aimusicplayer.service.UnifiedPlaybackService.createNotification(UnifiedPlaybackService.kt:602)
                                                                                                    	at com.example.aimusicplayer.service.UnifiedPlaybackService.onStartCommand(UnifiedPlaybackService.kt:269)
                                                                                                    	at android.app.ActivityThread.handleServiceArgs(ActivityThread.java:4656)
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleServiceArgs(Unknown Source:0)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2180)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 20:38:02.040  5778-5778  UnifiedPlaybackService  com.example.aimusicplayer            E  onStartCommand执行失败 (Ask Gemini)
                                                                                                    kotlin.UninitializedPropertyAccessException: lateinit property player has not been initialized
                                                                                                    	at com.example.aimusicplayer.service.UnifiedPlaybackService.createNotification(UnifiedPlaybackService.kt:602)
                                                                                                    	at com.example.aimusicplayer.service.UnifiedPlaybackService.onStartCommand(UnifiedPlaybackService.kt:269)
                                                                                                    	at android.app.ActivityThread.handleServiceArgs(ActivityThread.java:4656)
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleServiceArgs(Unknown Source:0)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2180)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 20:38:02.049  5778-5778  UnifiedPlaybackService  com.example.aimusicplayer            E  onStartCommand执行失败 (Ask Gemini)
                                                                                                    kotlin.UninitializedPropertyAccessException: lateinit property player has not been initialized
                                                                                                    	at com.example.aimusicplayer.service.UnifiedPlaybackService.createNotification(UnifiedPlaybackService.kt:602)
                                                                                                    	at com.example.aimusicplayer.service.UnifiedPlaybackService.onStartCommand(UnifiedPlaybackService.kt:269)
                                                                                                    	at android.app.ActivityThread.handleServiceArgs(ActivityThread.java:4656)
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleServiceArgs(Unknown Source:0)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2180)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 20:38:02.074  5778-5778  UnifiedPlaybackService  com.example.aimusicplayer            E  onStartCommand执行失败 (Ask Gemini)
                                                                                                    kotlin.UninitializedPropertyAccessException: lateinit property player has not been initialized
                                                                                                    	at com.example.aimusicplayer.service.UnifiedPlaybackService.createNotification(UnifiedPlaybackService.kt:602)
                                                                                                    	at com.example.aimusicplayer.service.UnifiedPlaybackService.onStartCommand(UnifiedPlaybackService.kt:269)
                                                                                                    	at android.app.ActivityThread.handleServiceArgs(ActivityThread.java:4656)
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleServiceArgs(Unknown Source:0)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2180)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 20:38:03.092  2506-3096  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-24 20:38:05.859   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:38:05.859   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:38:05.859   613-1680  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 20:38:08.001  1836-2396  bnan                    com.google.android.gms.persistent    E  Phenotype registration failed [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@242632114@24.26.32 (230800-650348549):53)
                                                                                                    	at bnan.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):68)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):47)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126)
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 20:38:17.928  2589-2914  Finsky                  com.android.vending                  E  [203] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abnf.run(PG:3)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.217.74:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 20:38:17.954  2589-2781  Finsky                  com.android.vending                  E  [188] iuw.a(52): Unexpected android-id = 0
2025-05-24 20:38:17.972  2589-2781  Finsky                  com.android.vending                  E  [188] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 20:38:28.244  3101-3179  Finsky                  com.android.vending                  E  [210] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abnf.run(PG:3)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.217.74:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 20:38:28.260  3101-3274  Finsky                  com.android.vending                  E  [228] iuw.a(52): Unexpected android-id = 0
2025-05-24 20:38:28.275  3101-3274  Finsky                  com.android.vending                  E  [228] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 20:38:31.892  2589-3277  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 20:38:31.919  2589-2916  Finsky                  com.android.vending                  E  [204] lua.a(218): Error when retrieving FCM instance id
2025-05-24 20:38:32.007  1542-5694  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 20:38:37.656  3174-3174  Finsky:background       com.android.vending                  E  [2] BackgroundCheckinReceiver.c(18): Receiver disabled.
2025-05-24 20:38:46.321  3101-3683  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 20:38:46.359  3101-3273  Finsky                  com.android.vending                  E  [227] lua.a(218): Error when retrieving FCM instance id
2025-05-24 20:39:33.203  3495-3495  Finsky:background       com.android.vending                  E  [2] BackgroundCheckinReceiver.c(18): Receiver disabled.
2025-05-24 20:39:34.671   174-174   keystore2               keystore2                            E  keystore2::remote_provisioning: In get_remote_provisioning_key_and_certs: Error occurred: In get_rem_prov_attest_key: Failed to get a key
                                                                                                    
                                                                                                    Caused by:
                                                                                                        0: In get_rem_prov_attest_key_helper: Failed to assign a key
                                                                                                        1: In assign_attestation_key: 
                                                                                                        2: In with_transaction.
                                                                                                        3: Out of keys.
                                                                                                        4: Error::Rc(ResponseCode(22))
2025-05-24 20:39:36.480   348-348   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData
2025-05-24 20:40:02.091  1542-1931  WakeLock                com.google.android.gms.persistent    E  *gms_scheduler*/com.google.android.gms/.phenotype.service.sync.PhenotypeConfigurator ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-24 20:40:17.344  1542-1931  WakeLock                com.google.android.gms.persistent    E  *gms_scheduler*/com.google.android.gms/.phenotype.service.sync.PhenotypeConfigurator ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-24 20:40:18.560  2589-2914  Finsky                  com.android.vending                  E  [203] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.regular. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.217.74:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 20:40:18.570  2589-2781  Finsky                  com.android.vending                  E  [188] iuw.a(52): Unexpected android-id = 0
2025-05-24 20:40:18.591  2589-2781  Finsky                  com.android.vending                  E  [188] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 20:40:31.074  1542-2090  NetworkScheduler.ATC    com.google.android.gms.persistent    E  Trying to release unacquired lock: com.google.android.gms/.phenotype.service.sync.PhenotypeConfigurator [CONTEXT service_id=218 ]
2025-05-24 20:40:31.078  1542-2090  NetworkScheduler.ATC    com.google.android.gms.persistent    E  Trying to release unacquired lock: com.google.android.gms/.phenotype.service.sync.PhenotypeConfigurator [CONTEXT service_id=218 ]
2025-05-24 20:40:33.223  1542-6326  PhenotypeFlagCommitter  com.google.android.gms.persistent    E  Retrieving snapshot for com.google.android.gms.playlog.uploader failed (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@242632114@24.26.32 (230800-650348549):53)
                                                                                                    	at bmyl.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):13)
                                                                                                    	at bmyl.j(:com.google.android.gms@242632114@24.26.32 (230800-650348549):42)
                                                                                                    	at bmyl.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):4)
                                                                                                    	at com.google.android.gms.clearcut.uploader.QosUploaderChimeraService.g(:com.google.android.gms@242632114@24.26.32 (230800-650348549):220)
                                                                                                    	at com.google.android.gms.clearcut.uploader.QosUploaderChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):24)
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 20:40:36.791   613-784   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 20:40:43.681  1542-6330  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire