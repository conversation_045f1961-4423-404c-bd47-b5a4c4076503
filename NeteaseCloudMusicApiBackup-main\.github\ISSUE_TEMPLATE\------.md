---
name: 创建问题反馈
about: 问题反馈
title: "<不按照模版格式或者删除模版信息将得不到处理,确认看完了调用前须知再发 issues>"
labels: ''
assignees: ''

---

## 环境
- 系统/平台: <你的系统和平台>

- nodejs 版本: <你的 NodeJS 版本号>

- API版本: <运行的云音乐 API 的版本号， 对应 package.json 里面的 version>

## 出现问题
<请清晰描述出现的问题,不要使用xxx不行之类的模糊不清的语句,我没有读心术>

## 重现步骤
<请清晰描述重现步骤，调用的接口链接和参数以及命令行错误信息,最好贴上截图(命令行错误信息和接口返回内容)>

## 期待效果
<请清晰描述现在的效果和期待的效果>  



---

>维护项目都是业余时间,精力有限,我只能挑容易解决的issues处理,为了节约双方时间,请尽可能提供足够的有用的信息,给的信息不够我只能根据精力和时间看情况处理,如果模板信息看都不看就删掉,我不会进行任何回复,并且一个月后close掉issue

>先看文档有没有相关说明,调用前须知必看,确认看完了调用前须知再发 issues

>先在 issues 搜一下是否有相似问题，没有再发,否则直接关闭

>不处理别人搭建的线上服务的问题,此项目不提供任何线上服务,请自行搭建

>重现步骤尽量详细,不能含糊不清，包含请求地址和对应参数以及操作过程描述,不是每个人都喜欢猜别人遇到了什么问题和找参数一个个试,也比较浪费时间

>如果不是提建议,提 issues 如果不照着模版来将不会优先处理或放着不管

>460 cheating 的问题把 `utils/request.js` 里面的 `headers['X-Real-IP']` 的注释取消掉就好
