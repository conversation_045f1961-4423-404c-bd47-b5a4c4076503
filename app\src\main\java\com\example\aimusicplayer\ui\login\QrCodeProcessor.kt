package com.example.aimusicplayer.ui.login

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.widget.ImageView
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.repository.UserRepository
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.qrcode.QRCodeWriter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import javax.inject.Inject

/**
 * 二维码处理器 - 参考ponymusic实现
 * 负责处理二维码登录相关的逻辑
 */
class QrCodeProcessor @Inject constructor(
    private val context: Context,
    private val userRepository: UserRepository
) {

    companion object {
        private const val TAG = "QrCodeProcessor"
        private const val QR_CHECK_INTERVAL = 3000L // 3秒检查一次二维码状态
        private const val MAX_RETRY_COUNT = 5 // 减少最大重试次数，避免一直重新加载
        private const val RETRY_DELAY = 1500L // 减少重试延迟时间
    }

    // 二维码状态 - 参考ponymusic的状态定义
    enum class QrStatus {
        LOADING,    // 加载中
        WAITING,    // 等待扫描 (801)
        SCANNED,    // 已扫描 (802)
        CONFIRMED,  // 已确认 (803)
        EXPIRED,    // 已过期 (800)
        ERROR       // 错误
    }

    // 二维码Bitmap
    private val _qrCodeBitmap = MutableLiveData<Bitmap?>()
    val qrCodeBitmap: LiveData<Bitmap?> = _qrCodeBitmap

    // 二维码图片URL (备用)
    private val _qrImageUrl = MutableLiveData<String>()
    val qrImageUrl: LiveData<String> = _qrImageUrl

    // 二维码状态
    private val _qrStatus = MutableLiveData<QrStatus>()
    val qrStatus: LiveData<QrStatus> = _qrStatus

    // 二维码Key
    private var qrKey: String? = null

    // 检查二维码状态的Job
    private var checkQrStatusJob: Job? = null

    // 重试计数
    private var retryCount = 0

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    /**
     * 获取二维码登录 - 参考ponymusic的完整实现
     */
    fun getLoginQrCode() {
        // 取消之前的任务
        checkQrStatusJob?.cancel()

        checkQrStatusJob = coroutineScope.launch(Dispatchers.Default) {
            retryCount = 0
            qrKey = ""
            _qrCodeBitmap.postValue(null)
            _qrImageUrl.postValue("")
            _qrStatus.postValue(QrStatus.LOADING)

            while (retryCount < MAX_RETRY_COUNT) {
                try {
                    // 步骤1: 获取二维码Key
                    val keyResponse = withContext(Dispatchers.IO) {
                        userRepository.getQrKey()
                    }

                    val keyJsonObject = JSONObject(keyResponse)
                    val keyCode = keyJsonObject.optInt("code")

                    if (keyCode != 200) {
                        Log.e(TAG, "获取二维码Key失败: ${keyJsonObject.optString("message")}")
                        retryAfterDelay()
                        continue
                    }

                    val keyData = keyJsonObject.optJSONObject("data")
                    if (keyData == null) {
                        Log.e(TAG, "获取二维码Key失败: 响应中没有data字段")
                        retryAfterDelay()
                        continue
                    }

                    qrKey = keyData.optString("unikey")
                    if (qrKey.isNullOrEmpty()) {
                        Log.e(TAG, "获取到的二维码Key为空")
                        retryAfterDelay()
                        continue
                    }

                    Log.d(TAG, "获取二维码Key成功: $qrKey")

                    // 步骤2: 获取二维码图片
                    val qrResponse = withContext(Dispatchers.IO) {
                        userRepository.getQrImage(qrKey!!)
                    }

                    val qrJsonObject = JSONObject(qrResponse)
                    val qrCode = qrJsonObject.optInt("code")

                    if (qrCode != 200) {
                        Log.e(TAG, "获取二维码图片失败: ${qrJsonObject.optString("message")}")
                        retryAfterDelay()
                        continue
                    }

                    val qrData = qrJsonObject.optJSONObject("data")
                    if (qrData == null) {
                        Log.e(TAG, "获取二维码图片失败: 响应中没有data字段")
                        retryAfterDelay()
                        continue
                    }

                    val qrUrl = qrData.optString("qrurl")
                    if (qrUrl.isNullOrEmpty()) {
                        Log.e(TAG, "获取到的二维码URL为空")
                        retryAfterDelay()
                        continue
                    }

                    Log.d(TAG, "获取二维码URL成功: $qrUrl")

                    // 步骤3: 生成二维码Bitmap
                    val qrBitmap = generateQrCodeBitmap(qrUrl)
                    if (qrBitmap != null) {
                        _qrCodeBitmap.postValue(qrBitmap)
                        _qrImageUrl.postValue(qrUrl)
                        _qrStatus.postValue(QrStatus.WAITING)

                        // 步骤4: 开始检查二维码状态
                        startCheckQrStatus()
                        return@launch
                    } else {
                        Log.e(TAG, "生成二维码Bitmap失败")
                        retryAfterDelay()
                        continue
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "获取二维码异常", e)
                    retryAfterDelay()
                    continue
                }
            }

            // 超过最大重试次数
            Log.e(TAG, "获取二维码失败，已达到最大重试次数")
            _qrStatus.postValue(QrStatus.ERROR)
        }
    }

    /**
     * 重试延迟 - 优化等待时间
     */
    private suspend fun retryAfterDelay() {
        retryCount++
        Log.d(TAG, "重试获取二维码，第 $retryCount 次，等待 ${RETRY_DELAY}ms")
        delay(RETRY_DELAY) // 使用更短的延迟时间
    }

    /**
     * 生成二维码Bitmap - 参考ponymusic实现
     */
    private fun generateQrCodeBitmap(qrUrl: String): Bitmap? {
        return try {
            val writer = QRCodeWriter()
            val hints = hashMapOf<EncodeHintType, Any>()
            hints[EncodeHintType.CHARACTER_SET] = "UTF-8"
            hints[EncodeHintType.MARGIN] = 1

            val bitMatrix = writer.encode(qrUrl, BarcodeFormat.QR_CODE, 512, 512, hints)
            val width = bitMatrix.width
            val height = bitMatrix.height
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)

            for (x in 0 until width) {
                for (y in 0 until height) {
                    bitmap.setPixel(x, y, if (bitMatrix[x, y]) android.graphics.Color.BLACK else android.graphics.Color.WHITE)
                }
            }

            Log.d(TAG, "生成二维码Bitmap成功，尺寸: ${width}x${height}")
            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "生成二维码Bitmap失败", e)
            null
        }
    }

    /**
     * 获取二维码图片URL
     */
    private suspend fun getQrImage(key: String?) {
        if (key.isNullOrEmpty()) {
            _qrStatus.value = QrStatus.ERROR
            return
        }

        try {
            // 调用API获取二维码图片URL
            val response = withContext(Dispatchers.IO) {
                userRepository.getQrImage(key)
            }

            // 解析响应
            val jsonObject = JSONObject(response)
            val code = jsonObject.optInt("code")

            if (code == 200) {
                val data = jsonObject.optJSONObject("data")
                if (data != null) {
                    val qrUrl = data.optString("qrimg")
                    Log.d(TAG, "获取二维码图片URL成功: $qrUrl")
                    _qrImageUrl.value = qrUrl
                    _qrStatus.value = QrStatus.WAITING

                    // 开始检查二维码状态
                    startCheckQrStatus()
                } else {
                    Log.e(TAG, "获取二维码图片URL失败: 响应中没有data字段")
                    _qrStatus.value = QrStatus.ERROR
                }
            } else {
                val message = jsonObject.optString("message", "获取二维码图片URL失败")
                Log.e(TAG, "获取二维码图片URL失败: $message")
                _qrStatus.value = QrStatus.ERROR
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取二维码图片URL异常", e)
            _qrStatus.value = QrStatus.ERROR
        }
    }

    /**
     * 开始检查二维码状态 - 参考ponymusic实现
     */
    private suspend fun startCheckQrStatus() {
        if (qrKey.isNullOrEmpty()) {
            Log.e(TAG, "二维码Key为空，无法检查状态")
            return
        }

        // 开始循环检查状态
        while (true) {
            try {
                val response = withContext(Dispatchers.IO) {
                    userRepository.checkQrStatus(qrKey!!)
                }

                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                Log.d(TAG, "检查二维码状态，code: $code")

                when (code) {
                    801 -> {
                        // 等待扫描
                        _qrStatus.postValue(QrStatus.WAITING)
                        delay(QR_CHECK_INTERVAL)
                    }
                    802 -> {
                        // 已扫描，等待确认
                        _qrStatus.postValue(QrStatus.SCANNED)
                        delay(QR_CHECK_INTERVAL)
                    }
                    803 -> {
                        // 授权登录成功 - 参考ponymusic实现
                        Log.d(TAG, "二维码登录成功")
                        _qrStatus.postValue(QrStatus.CONFIRMED)

                        // 获取cookie并保存 - 参考ponymusic的处理方式
                        val cookie = jsonObject.optString("cookie")
                        if (!cookie.isNullOrEmpty()) {
                            Log.d(TAG, "获取到cookie: ${cookie.length} 字符")
                            userRepository.saveCookie(cookie)

                            // 记录登录成功的额外信息
                            val nickname = jsonObject.optString("nickname", "")
                            val avatarUrl = jsonObject.optString("avatarUrl", "")
                            Log.d(TAG, "登录用户信息 - nickname: $nickname, avatarUrl: $avatarUrl")
                        } else {
                            Log.w(TAG, "二维码登录成功但未获取到cookie")
                        }
                        return
                    }
                    800 -> {
                        // 二维码过期
                        Log.d(TAG, "二维码已过期")
                        _qrStatus.postValue(QrStatus.EXPIRED)
                        return
                    }
                    else -> {
                        // 其他错误状态
                        val message = jsonObject.optString("message", "检查二维码状态失败")
                        Log.e(TAG, "检查二维码状态失败: $message")
                        _qrStatus.postValue(QrStatus.ERROR)
                        return
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查二维码状态异常", e)
                _qrStatus.postValue(QrStatus.ERROR)
                return
            }
        }
    }

    /**
     * 更新二维码状态
     */
    fun updateStatus(status: QrStatus) {
        _qrStatus.value = status
    }

    /**
     * 加载二维码图片到ImageView - 优先使用Bitmap
     */
    fun loadQrImage(imageView: ImageView) {
        val bitmap = _qrCodeBitmap.value
        if (bitmap != null) {
            imageView.setImageBitmap(bitmap)
        } else {
            // 备用方案：使用URL加载
            val imageUrl = _qrImageUrl.value
            if (!imageUrl.isNullOrEmpty()) {
                Glide.with(context)
                    .load(imageUrl)
                    .apply(RequestOptions()
                        .placeholder(R.drawable.ic_qr_placeholder)
                        .error(R.drawable.ic_qr_error)
                        .diskCacheStrategy(DiskCacheStrategy.ALL))
                    .into(imageView)
            } else {
                imageView.setImageResource(R.drawable.ic_qr_placeholder)
            }
        }
    }

    /**
     * 停止检查二维码状态
     */
    fun stopCheckQrStatus() {
        checkQrStatusJob?.cancel()
    }
}
