<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.aimusicplayer"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- 百度语音SDK所需权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- Android 13 (API 33) 及以上版本需要区分的存储权限 -->
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- Android 13 新增的精细存储权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- 应用所需的其他权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- 通知权限 (Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- 震动权限 -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- 唤醒锁权限 (ExoPlayer播放时保持设备唤醒) -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.CAMERA" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.screen.landscape"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />

    <permission
        android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.example.aimusicplayer.MusicApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="轻聆"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.AIMusicPlayer"
        android:usesCleartextTraffic="true" >

        <!-- 百度语音SDK必要的meta-data配置 -->
        <meta-data
            android:name="com.baidu.speech.APP_ID"
            android:value="118558442" />
        <meta-data
            android:name="com.baidu.speech.API_KEY"
            android:value="l07tTLiM8XdSVcM6Avmv5FG3" />
        <meta-data
            android:name="com.baidu.speech.SECRET_KEY"
            android:value="e4DxN5gewACp162txczyVRuJs4UGBhdb" />

        <!-- 启动页 -->
        <activity
            android:name="com.example.aimusicplayer.ui.splash.SplashActivity"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:screenOrientation="landscape"
            android:theme="@style/FullScreenTheme" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 登录页 -->
        <activity
            android:name="com.example.aimusicplayer.ui.login.LoginActivity"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:screenOrientation="landscape"
            android:theme="@style/FullScreenTheme" />

        <!-- 主界面（已重命名，移除了MainActivity2） -->
        <activity
            android:name="com.example.aimusicplayer.ui.main.MainActivity"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:screenOrientation="landscape"
            android:theme="@style/FullScreenTheme" />

        <!-- 播放服务 -->
        <service
            android:name="com.example.aimusicplayer.service.UnifiedPlaybackService"
            android:enabled="true"
            android:exported="false" />

        <!-- 播放器页面已迁移到Fragment，不再需要单独的Activity -->
        <!-- PlayerActivity已删除，功能已迁移到PlayerFragment -->

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.example.aimusicplayer.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name="com.karumi.dexter.DexterActivity"
            android:theme="@style/Dexter.Internal.Theme.Transparent" />
        <activity
            android:name="com.journeyapps.barcodescanner.CaptureActivity"
            android:clearTaskOnLaunch="true"
            android:screenOrientation="sensorLandscape"
            android:stateNotNeeded="true"
            android:theme="@style/zxing_CaptureTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />

        <meta-data
            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
            android:value="GlideModule" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.aimusicplayer.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>